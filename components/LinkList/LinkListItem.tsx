import { Paths } from "@/enum/Paths";
import { <PERSON> } from "expo-router";
import { Box } from "../ui/box";
import { ChevronRightIcon, Icon } from "../ui/icon";
import { Text } from "../ui/text";

interface Props {
  icon: React.ElementType;
  title: string;
  link: Paths;
}

export const LinkListItem = ({ icon, link, title }: Props) => {
  return (
    <Link
      href={link}
      className="bg-background-0 border-none w-full flex-row py-4 px-4 justify-between items-center"
    >
      <Box className="flex-row justify-between items-center w-full">
        <Box className="flex flex-row items-center gap-4">
          <Icon as={icon} size="lg" stroke="gray" fill={"gray"} />
          <Text className="font-normal text-typography-900" size="md">
            {title}
          </Text>
        </Box>
        <Icon as={ChevronRightIcon} size="lg" stroke={"gray"} />
      </Box>
    </Link>
  );
};

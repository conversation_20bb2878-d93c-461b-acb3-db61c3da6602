import { LinkList, LinkListItem } from "@/components/LinkList";
import { View } from "react-native";
import { LegalAndPrivacy } from "./_data";

export default function AccountScreen() {
  return (
    <View className="flex-1 items-start">
      <LinkList>
        {LegalAndPrivacy.content.map((item, index) => (
          <LinkListItem
            key={`legal-setting-${item.title}-${index}`}
            {...item}
          />
        ))}
      </LinkList>
    </View>
  );
}

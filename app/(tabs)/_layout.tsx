import { Tabs } from "expo-router";
import React from "react";
import { Platform } from "react-native";

import {
  AccountIcon,
  CompasIcon,
  Icon,
  OrganizerIcon,
} from "@/components/ui/icon";
import TabBarBackground from "@/components/ui/TabBarBackground";

export default function TabLayout() {
  return (
    <Tabs
      initialRouteName="explore"
      screenOptions={{
        headerShown: false,
        tabBarBackground: TabBarBackground,
        tabBarActiveTintColor: "black",
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: "absolute",
          },
          default: {},
        }),
      }}
    >
      <Tabs.Screen
        name="explore"
        options={{
          title: "Explore",
          tabBarIcon: ({ color }) => (
            <Icon as={CompasIcon} size="xl" fill={color} stroke={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="organizer"
        options={{
          title: "Organizer",
          tabBarIcon: ({ color }) => (
            <Icon as={OrganizerIcon} size="xl" fill={color} stroke={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="account"
        options={{
          title: "Account",
          href: "/account",
          tabBarIcon: ({ color }) => (
            <Icon as={AccountIcon} size="xl" fill={color} stroke={color} />
          ),
        }}
      />
    </Tabs>
  );
}

{"name": "climbalot", "main": "index.tsx", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "storybook": "EXPO_PUBLIC_STORYBOOK_ENABLED='true' expo start --web", "generate-tailwind-config": "node ./scripts/generateTailwind.config.js"}, "dependencies": {"@expo/html-elements": "^0.10.1", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/core": "^3.0.0", "@gluestack-ui/utils": "^3.0.0", "@gorhom/bottom-sheet": "^5.2.6", "@legendapp/motion": "^2.3.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "babel-plugin-module-resolver": "^5.0.2", "expo": "~53.0.22", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-router": "~5.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.11", "expo-web-browser": "~14.2.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-aria": "^3.33.0", "react-dom": "19.0.0", "react-native": "0.79.6", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^4.11.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-stately": "^3.39.0", "tailwind-variants": "^0.1.20", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-class-static-block": "^7.28.3", "@gluestack/storybook-addon": "^0.1.2", "@react-native-async-storage/async-storage": "^2.1.1", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/slider": "^4.5.6", "@storybook/addon-ondevice-actions": "^8.5.5", "@storybook/addon-ondevice-controls": "^8.5.5", "@storybook/react-native": "^8.5.5", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "babel-loader": "^8.4.1", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "raw-loader": "^4.0.2", "typescript": "~5.8.3"}, "private": true}
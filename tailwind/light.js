/* Semantic colors - Light */
module.exports = {
  theme: {
    extend: {
      colors: {
        "Primary-primary0": "#B3B3B3FF",
        "Primary-primary50": "#999999FF",
        "Primary-primary100": "#808080FF",
        "Primary-primary200": "#737373FF",
        "Primary-primary300": "#666666FF",
        "Primary-primary400": "#525252FF",
        "Primary-primary500": "#333333FF",
        "Primary-primary600": "#292929FF",
        "Primary-primary700": "#1F1F1FFF",
        "Primary-primary800": "#0D0D0DFF",
        "Primary-primary900": "#0A0A0AFF",
        "Primary-primary950": "#080808FF",
        "Secondary-secondary0": "#FDFDFDFF",
        "Secondary-secondary50": "#FBFBFBFF",
        "Secondary-secondary100": "#F6F6F6FF",
        "Secondary-secondary200": "#F2F2F2FF",
        "Secondary-secondary300": "#EDEDEDFF",
        "Secondary-secondary400": "#E6E6E7FF",
        "Secondary-secondary500": "#D9D9DBFF",
        "Secondary-secondary600": "#C6C7C7FF",
        "Secondary-secondary700": "#BDBDBDFF",
        "Secondary-secondary800": "#B1B1B1FF",
        "Secondary-secondary900": "#A5A4A4FF",
        "Secondary-secondary950": "#9D9D9DFF",
        "Tertiary-tertiary0": "#FFFAF5FF",
        "Tertiary-tertiary50": "#FFF2E5FF",
        "Tertiary-tertiary100": "#FFE9D5FF",
        "Tertiary-tertiary200": "#FED1AAFF",
        "Tertiary-tertiary300": "#FDB474FF",
        "Tertiary-tertiary400": "#FB9D4BFF",
        "Tertiary-tertiary500": "#E78128FF",
        "Tertiary-tertiary600": "#D7751FFF",
        "Tertiary-tertiary700": "#B4621AFF",
        "Tertiary-tertiary800": "#824917FF",
        "Tertiary-tertiary900": "#6C3D13FF",
        "Tertiary-tertiary950": "#543112FF",
        "Typography-typography0": "#FEFEFFFF",
        "Typography-typography50": "#F5F5F5FF",
        "Typography-typography100": "#E5E5E5FF",
        "Typography-typography200": "#DBDBDCFF",
        "Typography-typography300": "#D4D4D4FF",
        "Typography-typography400": "#A3A3A3FF",
        "Typography-typography500": "#8C8C8CFF",
        "Typography-typography600": "#737373FF",
        "Typography-typography700": "#525252FF",
        "Typography-typography800": "#404040FF",
        "Background-background-suplement-1": "#FFFFFFFF",
        "Typography-typography900": "#262627FF",
        "Background-background-suplement-2": "#F6F6F6FF",
        "Typography-typography950": "#171717FF",
        "Background-background-full": "#FFFFFFFF",
        "Background-background0": "#FFFFFFFF",
        "Background-background50": "#F6F6F6FF",
        "Background-background100": "#F2F2F2FF",
        "Background-background200": "#DBDBDBFF",
        "Background-background300": "#D5D5D5FF",
        "Background-background400": "#A3A3A3FF",
        "Background-background500": "#8E8E8EFF",
        "Background-background600": "#747474FF",
        "Background-background700": "#535353FF",
        "Background-background800": "#414141FF",
        "Background-background900": "#272727FF",
        "Background-background950": "#121212FF",
        "Background-background-muted": "#F7F7F7FF",
        "Border-border0": "#FDFEFEFF",
        "Border-border50": "#F3F3F3FF",
        "Border-border100": "#E6E6E6FF",
        "Border-border200": "#DDDCDBFF",
        "Border-border300": "#D3D3D3FF",
        "Border-border400": "#A5A3A3FF",
        "Border-border500": "#8C8D8DFF",
        "Border-border600": "#737474FF",
        "Border-border700": "#535252FF",
        "Border-border800": "#414141FF",
        "Border-border900": "#272624FF",
        "Border-border950": "#1A1717FF",
        "Success-success0": "#E4FFF4FF",
        "Success-success50": "#CAFFE8FF",
        "Success-success100": "#A2F1C0FF",
        "Success-success200": "#84D3A2FF",
        "Success-success300": "#66B584FF",
        "Success-success400": "#489766FF",
        "Success-success500": "#348352FF",
        "Success-success600": "#2A7948FF",
        "Success-success700": "#206F3EFF",
        "Success-success800": "#166534FF",
        "Success-success900": "#14532DFF",
        "Success-success950": "#1B3224FF",
        "Success-success-background": "#EDFCF2FF",
        "Error-error0": "#FEE9E9FF",
        "Error-error50": "#FEE2E2FF",
        "Error-error100": "#FECACAFF",
        "Error-error200": "#FCA5A5FF",
        "Error-error300": "#F87171FF",
        "Error-error400": "#EF4444FF",
        "Error-error500": "#E63535FF",
        "Error-error600": "#DC2626FF",
        "Error-error700": "#B91C1CFF",
        "Error-error800": "#991B1BFF",
        "Error-error900": "#7F1D1DFF",
        "Error-error950": "#531313FF",
        "Error-error-background": "#FEF1F1FF",
        "Warning-warning0": "#FFF9F5FF",
        "Warning-warning50": "#FFF4ECFF",
        "Warning-warning100": "#FFE7D5FF",
        "Warning-warning200": "#FECDAAFF",
        "Warning-warning300": "#FDAD74FF",
        "Warning-warning400": "#FB954BFF",
        "Warning-warning500": "#E77828FF",
        "Warning-warning600": "#D76C1FFF",
        "Warning-warning700": "#B45A1AFF",
        "Warning-warning800": "#824417FF",
        "Warning-warning900": "#6C3813FF",
        "Warning-warning950": "#542D12FF",
        "Warning-warning-background": "#FFF3EAFF",
        "Info-info0": "#ECF8FEFF",
        "Indicator-Indicator-primary": "#373737FF",
        "Indicator-Indicator-info": "#5399ECFF",
        "Info-info50": "#C7EBFCFF",
        "Indicator-Indicator-error": "#B91C1CFF",
        "Info-info100": "#A2DDFAFF",
        "Info-info200": "#7CCFF8FF",
        "Info-info300": "#57C2F6FF",
        "Info-info400": "#32B4F4FF",
        "Info-info500": "#0DA6F2FF",
        "Info-info600": "#0B8DCDFF",
        "Info-info700": "#0973A8FF",
        "Info-info800": "#075A83FF",
        "Info-info900": "#05405DFF",
        "Info-Info950": "#032638FF",
        "Info-info-background": "#EBF8FEFF",
      },
      spacing: {
        "Spacing-0": "0px",
        "Spacing-px": "1px",
        "Spacing-0point5": "2px",
        "Spacing-1": "4px",
        "Spacing-1point5": "6px",
        "Spacing-2": "8px",
        "Spacing-2point5": "10px",
        "Spacing-3": "12px",
        "Spacing-3point5": "14px",
        "Spacing-4": "16px",
        "Spacing-4point5": "18px",
        "Spacing-5": "20px",
        "Spacing-6": "24px",
        "Spacing-7": "28px",
        "Spacing-8": "32px",
        "Spacing-9": "36px",
        "Spacing-10": "40px",
        "Spacing-11": "44px",
        "Spacing-12": "48px",
        "Spacing-16": "64px",
        "Spacing-20": "80px",
        "Spacing-24": "96px",
        "Spacing-32": "128px",
        "Spacing-40": "160px",
        "Spacing-48": "192px",
        "Spacing-56": "224px",
        "Spacing-64": "256px",
        "Spacing-72": "288px",
        "Spacing-80": "320px",
        "Spacing-96": "384px",
        "Border-widths-0": "0px",
        "Border-widths-1": "1px",
        "Border-widths-2": "2px",
        "Border-widths-4": "4px",
        "Border-widths-8": "8px",
        "Border-radius-none": "0px",
        "Border-radius-xs": "2px",
        "Border-radius-sm": "4px",
        "Border-radius-md": "6px",
        "Border-radius-lg": "8px",
        "Border-radius-xl": "12px",
        "Border-radius-2xl": "16px",
        "Border-radius-3xl": "24px",
        "Border-radius-full": "9999px",
        "Opacity-0": "0px",
        "Opacity-5": "5px",
        "Opacity-10": "10px",
        "Opacity-20": "20px",
        "Opacity-25": "25px",
        "Opacity-30": "30px",
        "Opacity-40": "40px",
        "Opacity-50": "50px",
        "Opacity-60": "60px",
        "Opacity-70": "70px",
        "Opacity-75": "75px",
        "Opacity-80": "80px",
        "Opacity-90": "90px",
        "Opacity-95": "95px",
        "Opacity-100": "100px",
      },
    },
  },
};

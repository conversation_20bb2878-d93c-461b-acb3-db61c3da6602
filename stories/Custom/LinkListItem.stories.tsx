// LinkListItem.stories.tsx
import { LinkListItem } from "@/components/LinkList";
import { LegalIcon, PolicyIcon } from "@/components/ui/icon";
import { Paths } from "@/enum/Paths";
import React from "react";
import { View } from "react-native";

export default {
  title: "Custom/LinkListItem",
  component: LinkListItem,
};

export const Default = () => (
  <View style={{ padding: 20 }}>
    <LinkListItem icon={LegalIcon} title="Profile" link={Paths.ACCOUNT} />
  </View>
);

export const AnotherItem = () => (
  <View style={{ padding: 20 }}>
    <LinkListItem icon={PolicyIcon} title="Settings" link={Paths.ACCOUNT} />
  </View>
);
